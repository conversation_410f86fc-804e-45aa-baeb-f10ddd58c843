package com.dali.dali.local;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

public class QMSInput {

    /**
     * 读取Excel文件并将数据转换为List<Map>格式
     * @param filePath Excel文件路径
     * @return List<Map<String, Object>> 每一行数据作为一个Map，所有行组成List
     */
    public List<Map<String, Object>> readExcelToListMap(String filePath) {
        List<Map<String, Object>> dataList = new ArrayList<>();

        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {

            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);

            // 获取表头行
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                System.out.println("Excel文件为空或没有表头");
                return dataList;
            }

            // 读取表头
            List<String> headers = new ArrayList<>();
            for (Cell cell : headerRow) {
                headers.add(getCellValueAsString(cell));
            }

            // 验证表头是否包含期望的列
            validateHeaders(headers);

            // 读取数据行
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    continue; // 跳过空行
                }

                Map<String, Object> rowData = new HashMap<>();

                // 遍历每一列
                for (int cellIndex = 0; cellIndex < headers.size(); cellIndex++) {
                    Cell cell = row.getCell(cellIndex);
                    String columnName = headers.get(cellIndex);
                    Object cellValue = getCellValue(cell);
                    rowData.put(columnName, cellValue);
                }

                dataList.add(rowData);
            }

            System.out.println("成功读取Excel文件，共" + dataList.size() + "行数据");

        } catch (IOException e) {
            System.err.println("读取Excel文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("处理Excel文件时发生未知错误: " + e.getMessage());
            e.printStackTrace();
        }

        return dataList;
    }

    /**
     * 验证表头是否包含期望的列
     * @param headers 表头列表
     */
    private void validateHeaders(List<String> headers) {
        List<String> expectedHeaders = Arrays.asList("ip_address", "login_time", "user_name", "login_name");

        for (String expectedHeader : expectedHeaders) {
            if (!headers.contains(expectedHeader)) {
                System.out.println("警告: 未找到期望的列 '" + expectedHeader + "'");
            }
        }

        System.out.println("实际表头: " + headers);
    }

    /**
     * 获取单元格的值
     * @param cell 单元格
     * @return 单元格的值
     */
    private Object getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    // 如果是日期格式，返回格式化的日期字符串
                    Date date = cell.getDateCellValue();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    return sdf.format(date);
                } else {
                    // 如果是数字，检查是否为整数
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return (long) numericValue;
                    } else {
                        return numericValue;
                    }
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return null;
            default:
                return cell.toString();
        }
    }

    /**
     * 获取单元格的值作为字符串（主要用于表头）
     * @param cell 单元格
     * @return 单元格的字符串值
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    Date date = cell.getDateCellValue();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    return sdf.format(date);
                } else {
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return "";
            default:
                return cell.toString().trim();
        }
    }

    /**
     * 主方法，用于测试功能
     */
    public static void main(String[] args) {
        QMSInput qmsInput = new QMSInput();
        String filePath = "C:\\Users\\<USER>\\Desktop\\acs_login_log.xlsx";

        List<Map<String, Object>> dataList = qmsInput.readExcelToListMap(filePath);

        // 打印前几行数据作为示例


    }
}
